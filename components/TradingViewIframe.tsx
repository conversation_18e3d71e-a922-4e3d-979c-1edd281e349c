"use client";

import { useId, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { COINS } from "@/utils/coinList";

export default function TradingViewIframe() {
  const [symbol, setSymbol] = useState(COINS[0].tv);
  const id = useId(); // unique <iframe> id
  const encoded = encodeURIComponent(symbol);
  const src =
    `https://s.tradingview.com/widgetembed/?symbol=${encoded}` +
    "&interval=60&theme=dark&style=1&timezone=Etc%2FUTC" +
    "&hide_side_toolbar=0&withdateranges=1&saveimage=0&studies=[]" +
    `&frameElementId=${id}`;

  return (
    <div className="w-full max-w-6xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-4xl md:text-5xl font-heading font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent mb-4">
          Market Charts
        </h1>
        <p className="text-gray-400 text-lg">Real-time cryptocurrency market data</p>
      </div>
      {/* Coin Selector */}
      <div className="mb-6">
        <label htmlFor="crypto-select" className="block text-sm font-medium text-gray-300 mb-3">
          Select Asset
        </label>
        <Select value={symbol} onValueChange={setSymbol}>
          <SelectTrigger id="crypto-select" className="w-60 text-white">
            <SelectValue placeholder="Select a cryptocurrency" />
          </SelectTrigger>
          <SelectContent>
            {COINS.map(({ label, tv }) => (
              <SelectItem key={tv} value={tv}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* TradingView Chart */}
      <div className="relative overflow-hidden border border-gray-800/50 bg-gray-900/20 backdrop-blur-sm">
        <iframe
          key={symbol} /* forces re‑mount on change */
          id={id}
          src={src}
          width="100%"
          height="600"
          frameBorder="0"
          scrolling="no"
          allowFullScreen
          className="w-full"
          title={`TradingView Chart for ${COINS.find((coin) => coin.tv === symbol)?.label || "Cryptocurrency"}`}
        />
      </div>
    </div>
  );
}
