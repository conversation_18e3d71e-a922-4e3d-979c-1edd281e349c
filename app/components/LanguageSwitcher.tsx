"use client";

import { Globe } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";

const LanguageSwitcher = () => {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const switchLanguage = (newLocale: string) => {
    // Set the NEXT_LOCALE cookie (1 year expiry)
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000`;
    // Remove the current locale from the pathname if present
    const pathWithoutLocale = pathname.replace(new RegExp(`^/(${locale})(/|$)`), "/");
    // Navigate to the new locale route (or reload if on /)
    if (pathWithoutLocale === "/" || pathWithoutLocale === "") {
      router.push("/"); // fallback to root
    } else {
      router.push(`/${newLocale}${pathWithoutLocale}`);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Globe className="w-4 h-4 text-gray-400" />
      <div className="flex gap-2">
        <button
          onClick={() => switchLanguage("en")}
          className={`px-2 py-1 text-sm rounded transition-colors ${
            locale === "en" ? "bg-blue-600 text-white" : "text-gray-400 hover:text-white"
          }`}
          type="button"
        >
          EN
        </button>
        <button
          onClick={() => switchLanguage("vi")}
          className={`px-2 py-1 text-sm rounded transition-colors ${
            locale === "vi" ? "bg-blue-600 text-white" : "text-gray-400 hover:text-white"
          }`}
          type="button"
        >
          VI
        </button>
      </div>
    </div>
  );
};

export default LanguageSwitcher;
