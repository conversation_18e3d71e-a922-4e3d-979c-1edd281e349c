import { Link, TrendingDown, TrendingUp, Users } from "lucide-react";

const PerformanceStats = () => {
  const stats = [
    {
      icon: TrendingUp,
      value: "+21.5%",
      label: "avg. monthly ROI across all bots",
      color: "text-blue-400",
      bgColor: "bg-blue-500/10",
      borderColor: "border-blue-500/30",
    },
    {
      icon: Users,
      value: "5,200+",
      label: "active users",
      color: "text-amber-400",
      bgColor: "bg-amber-500/10",
      borderColor: "border-amber-500/30",
    },
    {
      icon: Link,
      value: "3",
      label: "major exchanges supported (Binance, Bybit, OKX)",
      color: "text-purple-400",
      bgColor: "bg-purple-500/10",
      borderColor: "border-purple-500/30",
    },
    {
      icon: TrendingDown,
      value: "-32%",
      label: "average drawdown vs market -47%",
      color: "text-green-400",
      bgColor: "bg-green-500/10",
      borderColor: "border-green-500/30",
    },
  ];

  return (
    <section className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="font-heading text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in-up">
            Our Numbers Speak for Themselves
          </h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                // biome-ignore lint/suspicious/noArrayIndexKey: stats array order won't change
                key={index}
                className={`group text-center p-8 rounded-3xl bg-gray-900/50 border ${stat.borderColor} hover:bg-gray-800/50 transition-all duration-500 animate-scale-in`}
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div
                  className={`w-16 h-16 ${stat.bgColor} border ${stat.borderColor} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                >
                  <Icon className={`w-8 h-8 ${stat.color}`} />
                </div>

                <div
                  className={`font-heading text-4xl font-bold ${stat.color} mb-2 group-hover:scale-110 transition-transform duration-300`}
                >
                  {stat.value}
                </div>

                <p className="font-body text-gray-400 text-sm leading-relaxed">{stat.label}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default PerformanceStats;
