import { <PERSON><PERSON><PERSON><PERSON>, BookO<PERSON>, Calendar, Clock, Tag, User } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import ShareButton from "@/app/components/ShareButton";
import { getAllPosts, getPostBySlug, type Post } from "@/lib/blog";
import { formatDate } from "@/lib/date";

const BlogDetailPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const { slug } = await params;
  let post: Post;
  try {
    post = await getPostBySlug(slug);
  } catch {
    notFound();
  }

  const allPosts = await getAllPosts();
  const relatedPosts = allPosts.filter((p) => p.slug !== slug).slice(0, 3);

  return (
    <article className="pt-20 bg-black min-h-screen">
      {/* Hero Section */}
      <div className="relative h-96 bg-gradient-to-r from-black to-gray-900 overflow-hidden">
        <img
          src={post.thumbnail}
          alt={post.title}
          className="absolute inset-0 w-full h-full object-cover opacity-30"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-gray-900/80" />
        <div className="relative z-10 max-w-4xl mx-auto px-6 h-full flex items-center">
          <div>
            <Link
              href="/blog"
              className="inline-flex items-center gap-2 text-blue-400 hover:text-blue-300 transition-colors mb-6"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Blog
            </Link>
            <h1 className="font-heading text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              {post.title}
            </h1>
            <div className="flex items-center gap-6 font-body text-gray-300">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(post.date)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{post.readTime}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="bg-gray-900">
        <div className="max-w-4xl mx-auto px-6 py-16">
          {/* Share Buttons */}
          <div className="flex items-center justify-between mb-12 pb-6 border-b border-gray-800">
            <div className="flex items-center gap-4">
              <span className="text-gray-400 font-medium">Share this article:</span>
              <div className="flex gap-2">
                <ShareButton />
              </div>
            </div>
            <div className="flex items-center gap-2 text-gray-400">
              <BookOpen className="w-4 h-4" />
              <span className="text-sm">{post.readTime}</span>
            </div>
          </div>
          <div
            className="prose prose-lg max-w-none prose-headings:text-white prose-headings:font-bold prose-p:text-gray-300 prose-p:leading-relaxed prose-a:text-blue-400 prose-a:no-underline hover:prose-a:underline prose-strong:text-white prose-ul:text-gray-300 prose-ol:text-gray-300 prose-li:text-gray-300 prose-li:marker:text-blue-400 prose-blockquote:border-blue-500 prose-blockquote:bg-gray-800 prose-blockquote:p-6 prose-blockquote:rounded-xl"
            // biome-ignore lint/security/noDangerouslySetInnerHtml: content is sanitized beforehand
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
          {/* Tags */}
          <div className="mt-12 pt-8 border-t border-gray-800">
            <div className="flex items-center gap-2 mb-4">
              <Tag className="w-5 h-5 text-gray-400" />
              <span className="font-medium text-white">Tags:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-gray-800 text-gray-300 rounded-full text-sm hover:bg-blue-500/20 hover:text-blue-400 transition-colors cursor-pointer border border-gray-700"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <div className="bg-black py-16">
          <div className="max-w-7xl mx-auto px-6">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Related Articles</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {relatedPosts.map((p, index) => (
                <Link
                  key={p.slug}
                  href={`/blog/${p.slug}`}
                  className="group bg-gray-900 border border-gray-800 rounded-2xl shadow-lg hover:border-gray-700 hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 animate-scale-in"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <img
                    src={p.thumbnail}
                    alt={p.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-3 group-hover:text-blue-400 transition-colors line-clamp-2">
                      {p.title}
                    </h3>
                    <p className="text-sm text-gray-400">{formatDate(p.date)}</p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
        <div className="max-w-4xl mx-auto px-6 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">Ready to Try AlphaX?</h2>
          <p className="text-xl mb-8 opacity-90">
            Experience the AI trading bot featured in this article with a 7-day free trial.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              type="button"
            >
              Start Free Trial
            </button>
            <button
              className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300"
              type="button"
            >
              View AlphaX Performance
            </button>
          </div>
        </div>
      </div>
    </article>
  );
};

export default BlogDetailPage;
